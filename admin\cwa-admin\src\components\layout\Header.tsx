'use client';
import { useAuth } from '@/context/AuthContext';
import { usePathname } from 'next/navigation';
import {
  BellIcon,
  ChevronDownIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';

interface HeaderProps {
  onMenuClick: () => void;
}

export default function Header({ onMenuClick }: HeaderProps) {
  const { user } = useAuth();
  const pathname = usePathname();

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getPageTitle = () => {
    switch (pathname) {
      case '/dashboard':
        return 'Dashboard';
      case '/users':
        return 'User Management';
      case '/products':
        return 'Product Management';
      case '/orders':
        return 'Order Management';
      case '/settings':
        return 'Settings';
      default:
        return 'CWA Admin Dashboard';
    }
  };

  return (
    <header
      className="h-14 sm:h-16 flex items-center px-3 sm:px-4 md:px-6 border-b"
      style={{
        backgroundColor: 'var(--header-background)',
        boxShadow: 'var(--header-shadow)',
        borderColor: 'var(--border-color)'
      }}
    >
      {/* Mobile Menu Button */}
      <button
        onClick={onMenuClick}
        className="lg:hidden p-1.5 sm:p-2 rounded-lg text-slate-600 hover:text-slate-800 hover:bg-slate-100 transition-colors mr-2 sm:mr-3"
      >
        <Bars3Icon className="w-5 h-5 sm:w-6 sm:h-6" />
      </button>

      <div className="flex-1 min-w-0">
        <h2 className="text-base sm:text-lg md:text-xl font-semibold truncate" style={{ color: 'var(--foreground)' }}>
          {getPageTitle()}
        </h2>
        <p className="text-xs sm:text-sm text-slate-500 mt-0.5 hidden sm:block">
          Welcome back to your admin dashboard
        </p>
      </div>

      <div className="flex items-center space-x-1 sm:space-x-2 md:space-x-4">
        {/* Notifications */}
        <button className="relative p-1.5 sm:p-2 text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-lg transition-colors">
          <BellIcon className="w-4 h-4 sm:w-5 sm:h-5" />
          <span className="absolute top-0.5 sm:top-1 right-0.5 sm:right-1 w-1.5 h-1.5 sm:w-2 sm:h-2 bg-red-500 rounded-full"></span>
        </button>

        {/* User Profile */}
        <div className="relative">
          <button className="flex items-center space-x-1 sm:space-x-2 md:space-x-3 text-slate-700 hover:text-slate-900 focus:outline-none p-1.5 sm:p-2 rounded-lg hover:bg-slate-100 transition-colors">
            <div className="text-right hidden sm:block">
              <p className="text-xs sm:text-sm font-medium">{user?.name || 'Admin User'}</p>
              <p className="text-xs text-slate-500 hidden md:block">Administrator</p>
            </div>
            <div className="w-7 h-7 sm:w-8 sm:h-8 md:w-10 md:h-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold shadow-lg">
              <span className="text-xs sm:text-sm">
                {user?.name ? getInitials(user.name) : 'AU'}
              </span>
            </div>
            <ChevronDownIcon className="w-3 h-3 sm:w-4 sm:h-4 hidden sm:block" />
          </button>
        </div>
      </div>
    </header>
  );
}
