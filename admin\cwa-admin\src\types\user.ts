export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  isAdmin?: boolean;
  createdAt: string;
  updatedAt?: string;
  avatar?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  setAuthState: (state: AuthState) => void;
}

// Product related types
export interface Product {
  _id: string;
  name: string;
  type: 'regular' | 'featured' | 'sale' | 'new';
  price: number;
  image: string;
  description: string;
  quantity: number;
  stock: boolean;
  category: string;
  images: string[];
  video?: string;
  discount?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ProductsResponse {
  status: string;
  results: number;
  data: {
    products: Product[];
  };
}
